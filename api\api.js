// API接口封装 - 二手手机库存管理系统
// 使用 RESTful 风格，支持 Mock 数据模拟

class API {
    constructor() {
        this.baseURL = '/api';
        this.useMock = true; // 开发阶段使用Mock数据
    }

    // 通用请求方法
    async request(endpoint, options = {}) {
        const url = this.useMock ? `/mock${endpoint}` : `${this.baseURL}${endpoint}`;

        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            },
        };

        const config = { ...defaultOptions, ...options };

        try {
            const response = await fetch(url, config);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    }

    // GET 请求
    async get(endpoint, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = queryString ? `${endpoint}?${queryString}` : endpoint;

        return this.request(url, {
            method: 'GET',
        });
    }

    // POST 请求
    async post(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data),
        });
    }

    // PUT 请求
    async put(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data),
        });
    }

    // DELETE 请求
    async delete(endpoint) {
        return this.request(endpoint, {
            method: 'DELETE',
        });
    }

    // ==================== 顾客端API ====================

    // 获取商品列表
    async getProducts(filters = {}) {
        return this.get('/products', filters);
    }

    // 获取商品详情
    async getProductDetail(productId) {
        return this.get(`/products/${productId}`);
    }

    // 搜索商品
    async searchProducts(keyword, filters = {}) {
        return this.get('/products/search', { keyword, ...filters });
    }

    // ==================== 商家端API ====================

    // 商家登录验证
    async merchantLogin(pinCode) {
        return this.post('/merchant/login', { pinCode });
    }

    // 获取商家工作台数据
    async getDashboardData() {
        return this.get('/merchant/dashboard');
    }

    // 获取库存列表
    async getInventory(filters = {}) {
        return this.get('/merchant/inventory', filters);
    }

    // 添加库存商品
    async addInventoryItem(productData) {
        return this.post('/merchant/inventory', productData);
    }

    // 更新库存商品
    async updateInventoryItem(itemId, productData) {
        return this.put(`/merchant/inventory/${itemId}`, productData);
    }

    // 删除库存商品
    async deleteInventoryItem(itemId) {
        return this.delete(`/merchant/inventory/${itemId}`);
    }

    // 获取销售统计数据
    async getSalesStats(period = '7d') {
        return this.get('/merchant/stats/sales', { period });
    }

    // 获取库存统计数据
    async getInventoryStats() {
        return this.get('/merchant/stats/inventory');
    }

    // 获取商家信息
    async getMerchantProfile() {
        return this.get('/merchant/profile');
    }

    // 更新商家信息
    async updateMerchantProfile(profileData) {
        return this.put('/merchant/profile', profileData);
    }

    // 获取活动日志
    async getActivityLog(limit = 10) {
        return this.get('/merchant/activity', { limit });
    }

    // 导出报表
    async exportReport(type, period) {
        return this.get('/merchant/export', { type, period });
    }

    // ==================== 通用工具方法 ====================

    // 格式化价格
    formatPrice(price) {
        return `¥${price.toLocaleString()}`;
    }

    // 格式化日期
    formatDate(date) {
        return new Date(date).toLocaleDateString('zh-CN');
    }

    // 格式化时间
    formatDateTime(date) {
        return new Date(date).toLocaleString('zh-CN');
    }

    // 计算时间差
    getTimeAgo(date) {
        const now = new Date();
        const past = new Date(date);
        const diffInSeconds = Math.floor((now - past) / 1000);

        if (diffInSeconds < 60) {
            return '刚刚';
        } else if (diffInSeconds < 3600) {
            return `${Math.floor(diffInSeconds / 60)}分钟前`;
        } else if (diffInSeconds < 86400) {
            return `${Math.floor(diffInSeconds / 3600)}小时前`;
        } else {
            return `${Math.floor(diffInSeconds / 86400)}天前`;
        }
    }

    // 生成商品状态标签
    getStatusBadge(status) {
        const statusMap = {
            'available': { text: '在售', class: 'status-available' },
            'reserved': { text: '已预订', class: 'status-reserved' },
            'sold': { text: '已售出', class: 'status-sold' }
        };
        return statusMap[status] || { text: '未知', class: 'status-unknown' };
    }

    // 生成成色标签
    getConditionBadge(condition) {
        const conditionMap = {
            99: { text: '99新', class: 'bg-green-100 text-green-800' },
            98: { text: '98新', class: 'bg-green-100 text-green-800' },
            95: { text: '95新', class: 'bg-yellow-100 text-yellow-800' },
            90: { text: '90新', class: 'bg-orange-100 text-orange-800' },
            85: { text: '85新', class: 'bg-red-100 text-red-800' }
        };
        return conditionMap[condition] || { text: '未知', class: 'bg-gray-100 text-gray-800' };
    }
}

// 创建全局API实例
const api = new API();

// 导出API实例
if (typeof module !== 'undefined' && module.exports) {
    module.exports = api;
} else {
    window.api = api;
}