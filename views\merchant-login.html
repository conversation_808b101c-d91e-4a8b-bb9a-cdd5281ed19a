<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商家登录 - 安全验证</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 渐变背景 */
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        /* PIN码输入框样式 */
        .pin-input {
            width: 50px;
            height: 50px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
        }
        
        .pin-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: white;
            outline: none;
        }
        
        .pin-input.filled {
            border-color: #10b981;
            background: #f0fdf4;
            color: #059669;
        }
        
        .pin-input.error {
            border-color: #ef4444;
            background: #fef2f2;
            animation: shake 0.5s ease-in-out;
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        
        /* 登录卡片样式 */
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        /* 品牌Logo样式 */
        .brand-logo {
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        /* 安全指示器 */
        .security-indicator {
            background: linear-gradient(135deg, #10b981, #059669);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        /* 数字键盘样式 */
        .keypad-button {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            font-size: 24px;
            font-weight: bold;
            color: #374151;
            transition: all 0.2s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .keypad-button:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }
        
        .keypad-button:active {
            transform: translateY(0);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .keypad-button.special {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }
    </style>
</head>
<body class="gradient-bg flex items-center justify-center p-4">
    <div class="w-full max-w-sm">
        <!-- 登录卡片 -->
        <div class="login-card rounded-3xl p-8 text-center">
            <!-- 品牌Logo和标题 -->
            <div class="mb-8">
                <div class="flex items-center justify-center mb-4">
                    <div class="security-indicator w-16 h-16 rounded-full flex items-center justify-center">
                        <i class="fas fa-shield-alt text-white text-2xl"></i>
                    </div>
                </div>
                <h1 class="brand-logo text-3xl font-bold mb-2">精品二手机</h1>
                <p class="text-gray-600 text-sm">商家安全登录</p>
            </div>

            <!-- PIN码输入区域 -->
            <div class="mb-8">
                <p class="text-gray-700 font-medium mb-6">请输入6位安全PIN码</p>
                <div class="flex justify-center space-x-3 mb-4">
                    <input type="password" class="pin-input" maxlength="1" id="pin1" readonly>
                    <input type="password" class="pin-input" maxlength="1" id="pin2" readonly>
                    <input type="password" class="pin-input" maxlength="1" id="pin3" readonly>
                    <input type="password" class="pin-input" maxlength="1" id="pin4" readonly>
                    <input type="password" class="pin-input" maxlength="1" id="pin5" readonly>
                    <input type="password" class="pin-input" maxlength="1" id="pin6" readonly>
                </div>
                
                <!-- 错误提示 -->
                <div id="errorMessage" class="text-red-500 text-sm font-medium opacity-0 transition-opacity duration-300">
                    PIN码错误，请重新输入
                </div>
            </div>

            <!-- 数字键盘 -->
            <div class="grid grid-cols-3 gap-4 mb-6">
                <button class="keypad-button" onclick="inputPin('1')">1</button>
                <button class="keypad-button" onclick="inputPin('2')">2</button>
                <button class="keypad-button" onclick="inputPin('3')">3</button>
                <button class="keypad-button" onclick="inputPin('4')">4</button>
                <button class="keypad-button" onclick="inputPin('5')">5</button>
                <button class="keypad-button" onclick="inputPin('6')">6</button>
                <button class="keypad-button" onclick="inputPin('7')">7</button>
                <button class="keypad-button" onclick="inputPin('8')">8</button>
                <button class="keypad-button" onclick="inputPin('9')">9</button>
                <button class="keypad-button special" onclick="clearPin()">
                    <i class="fas fa-backspace"></i>
                </button>
                <button class="keypad-button" onclick="inputPin('0')">0</button>
                <button class="keypad-button special" onclick="submitPin()">
                    <i class="fas fa-check"></i>
                </button>
            </div>

            <!-- 安全提示 -->
            <div class="text-xs text-gray-500 space-y-1">
                <div class="flex items-center justify-center space-x-1">
                    <i class="fas fa-lock text-green-500"></i>
                    <span>端到端加密保护</span>
                </div>
                <div class="flex items-center justify-center space-x-1">
                    <i class="fas fa-eye-slash text-blue-500"></i>
                    <span>PIN码本地验证</span>
                </div>
            </div>
        </div>

        <!-- 底部帮助 -->
        <div class="text-center mt-6">
            <p class="text-white text-sm opacity-80">
                忘记PIN码？请联系系统管理员
            </p>
        </div>
    </div>

    <script>
        let currentPin = '';
        const correctPin = '123456'; // 演示用PIN码，实际应用中应该从服务器验证
        let currentIndex = 0;

        // 输入PIN码数字
        function inputPin(digit) {
            if (currentPin.length < 6) {
                currentPin += digit;
                currentIndex++;
                
                // 更新输入框显示
                const pinInput = document.getElementById(`pin${currentIndex}`);
                pinInput.value = '●';
                pinInput.classList.add('filled');
                
                // 如果输入完6位，自动提交
                if (currentPin.length === 6) {
                    setTimeout(submitPin, 300);
                }
            }
        }

        // 清除最后一位
        function clearPin() {
            if (currentPin.length > 0) {
                const pinInput = document.getElementById(`pin${currentIndex}`);
                pinInput.value = '';
                pinInput.classList.remove('filled', 'error');
                
                currentPin = currentPin.slice(0, -1);
                currentIndex--;
            }
        }

        // 提交PIN码验证
        function submitPin() {
            if (currentPin.length !== 6) {
                showError('请输入完整的6位PIN码');
                return;
            }

            // 模拟验证过程
            showLoading();
            
            setTimeout(() => {
                if (currentPin === correctPin) {
                    // 验证成功
                    showSuccess();
                    setTimeout(() => {
                        window.location.href = 'merchant-dashboard.html';
                    }, 1500);
                } else {
                    // 验证失败
                    showError('PIN码错误，请重新输入');
                    resetPin();
                }
            }, 1000);
        }

        // 显示错误信息
        function showError(message) {
            const errorElement = document.getElementById('errorMessage');
            errorElement.textContent = message;
            errorElement.classList.remove('opacity-0');
            errorElement.classList.add('opacity-100');
            
            // 输入框显示错误状态
            for (let i = 1; i <= 6; i++) {
                const pinInput = document.getElementById(`pin${i}`);
                pinInput.classList.add('error');
            }
            
            // 3秒后隐藏错误信息
            setTimeout(() => {
                errorElement.classList.remove('opacity-100');
                errorElement.classList.add('opacity-0');
            }, 3000);
        }

        // 显示加载状态
        function showLoading() {
            const submitButton = document.querySelector('.keypad-button.special:last-child');
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            submitButton.disabled = true;
        }

        // 显示成功状态
        function showSuccess() {
            const submitButton = document.querySelector('.keypad-button.special:last-child');
            submitButton.innerHTML = '<i class="fas fa-check text-green-500"></i>';
            
            // 输入框显示成功状态
            for (let i = 1; i <= 6; i++) {
                const pinInput = document.getElementById(`pin${i}`);
                pinInput.classList.remove('error');
                pinInput.classList.add('filled');
                pinInput.style.borderColor = '#10b981';
                pinInput.style.backgroundColor = '#f0fdf4';
            }
        }

        // 重置PIN码输入
        function resetPin() {
            currentPin = '';
            currentIndex = 0;
            
            for (let i = 1; i <= 6; i++) {
                const pinInput = document.getElementById(`pin${i}`);
                pinInput.value = '';
                pinInput.classList.remove('filled', 'error');
            }
            
            // 重置提交按钮
            const submitButton = document.querySelector('.keypad-button.special:last-child');
            submitButton.innerHTML = '<i class="fas fa-check"></i>';
            submitButton.disabled = false;
        }

        // 键盘事件支持
        document.addEventListener('keydown', (e) => {
            if (e.key >= '0' && e.key <= '9') {
                inputPin(e.key);
            } else if (e.key === 'Backspace') {
                clearPin();
            } else if (e.key === 'Enter') {
                submitPin();
            }
        });

        // 防止页面刷新时丢失状态
        window.addEventListener('beforeunload', (e) => {
            if (currentPin.length > 0) {
                e.preventDefault();
                e.returnValue = '';
            }
        });
    </script>
</body>
</html>
