<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商家工作台 - 精品二手机</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 数据卡片样式 */
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: 800;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        /* 智能提醒卡片 */
        .alert-card {
            border-left: 4px solid;
            transition: all 0.3s ease;
        }
        
        .alert-card:hover {
            transform: translateX(4px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .alert-warning {
            border-left-color: #f59e0b;
            background: linear-gradient(135deg, #fef3c7, #fde68a);
        }
        
        .alert-info {
            border-left-color: #3b82f6;
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
        }
        
        .alert-success {
            border-left-color: #10b981;
            background: linear-gradient(135deg, #d1fae5, #a7f3d0);
        }
        
        /* 悬浮操作按钮 */
        .fab {
            position: fixed;
            bottom: 80px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            border-radius: 50%;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
            transition: all 0.3s ease;
            z-index: 1000;
        }
        
        .fab:hover {
            transform: scale(1.1) rotate(90deg);
            box-shadow: 0 12px 35px rgba(255, 107, 107, 0.6);
        }
        
        /* 活动流样式 */
        .activity-item {
            transition: all 0.2s ease;
        }
        
        .activity-item:hover {
            background-color: #f9fafb;
            transform: translateX(4px);
        }
        
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
        }
        
        .activity-in {
            background: linear-gradient(135deg, #10b981, #059669);
        }
        
        .activity-out {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }
        
        .activity-update {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
        }
        
        /* 底部导航栏 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            max-width: 448px;
            width: 100%;
            background: white;
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .nav-item {
            transition: all 0.2s ease;
        }
        
        .nav-item.active {
            color: #667eea;
        }
        
        .nav-item:not(.active):hover {
            color: #9ca3af;
        }
    </style>
</head>
<body class="bg-gray-50 pb-20">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm">
        <div class="max-w-md mx-auto px-4 py-4 flex items-center justify-between">
            <div>
                <h1 class="text-xl font-bold text-gray-800">工作台</h1>
                <p class="text-sm text-gray-500">今天是个好日子，加油！</p>
            </div>
            <div class="flex items-center space-x-3">
                <button class="p-2 rounded-full hover:bg-gray-100 transition-colors relative">
                    <i class="fas fa-bell text-gray-600 text-lg"></i>
                    <span class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">3</span>
                </button>
                <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                    <i class="fas fa-user text-white text-sm"></i>
                </div>
            </div>
        </div>
    </header>

    <!-- 核心数据卡片 -->
    <div class="max-w-md mx-auto px-4 py-4">
        <div class="grid grid-cols-3 gap-3 mb-6">
            <div class="stats-card text-white p-4 rounded-xl text-center">
                <div class="stats-number">3</div>
                <div class="text-sm opacity-90">今日入库</div>
            </div>
            <div class="stats-card text-white p-4 rounded-xl text-center">
                <div class="stats-number">1</div>
                <div class="text-sm opacity-90">今日售出</div>
            </div>
            <div class="stats-card text-white p-4 rounded-xl text-center">
                <div class="stats-number">128</div>
                <div class="text-sm opacity-90">总库存</div>
            </div>
        </div>
    </div>

    <!-- 智能提醒区 -->
    <div class="max-w-md mx-auto px-4 mb-6">
        <h2 class="text-lg font-semibold text-gray-800 mb-3">智能提醒</h2>
        <div class="space-y-3">
            <div class="alert-card alert-warning p-4 rounded-lg">
                <div class="flex items-start space-x-3">
                    <i class="fas fa-exclamation-triangle text-orange-600 mt-1"></i>
                    <div>
                        <h3 class="font-medium text-gray-800">库存预警</h3>
                        <p class="text-sm text-gray-600">iPhone 12 Pro 仅剩 1 台，建议及时补货</p>
                    </div>
                </div>
            </div>
            
            <div class="alert-card alert-info p-4 rounded-lg">
                <div class="flex items-start space-x-3">
                    <i class="fas fa-chart-line text-blue-600 mt-1"></i>
                    <div>
                        <h3 class="font-medium text-gray-800">热门机型</h3>
                        <p class="text-sm text-gray-600">最近7天 iPhone 14 Pro 咨询量最高</p>
                    </div>
                </div>
            </div>
            
            <div class="alert-card alert-success p-4 rounded-lg">
                <div class="flex items-start space-x-3">
                    <i class="fas fa-clock text-green-600 mt-1"></i>
                    <div>
                        <h3 class="font-medium text-gray-800">长期未售出</h3>
                        <p class="text-sm text-gray-600">华为 Mate 40 Pro 已在库 60 天，考虑调价</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 近期动态 -->
    <div class="max-w-md mx-auto px-4 mb-6">
        <h2 class="text-lg font-semibold text-gray-800 mb-3">近期动态</h2>
        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
            <div class="activity-item p-4 border-b border-gray-100 flex items-center space-x-3">
                <div class="activity-icon activity-in">
                    <i class="fas fa-plus"></i>
                </div>
                <div class="flex-1">
                    <p class="text-sm font-medium text-gray-800">入库：iPhone 14 Pro 256G 紫色</p>
                    <p class="text-xs text-gray-500">入库价: ¥5,500 · 2小时前</p>
                </div>
            </div>
            
            <div class="activity-item p-4 border-b border-gray-100 flex items-center space-x-3">
                <div class="activity-icon activity-out">
                    <i class="fas fa-minus"></i>
                </div>
                <div class="flex-1">
                    <p class="text-sm font-medium text-gray-800">售出：小米 13 Ultra 512G</p>
                    <p class="text-xs text-gray-500">售价: ¥4,800 · 4小时前</p>
                </div>
            </div>
            
            <div class="activity-item p-4 border-b border-gray-100 flex items-center space-x-3">
                <div class="activity-icon activity-update">
                    <i class="fas fa-edit"></i>
                </div>
                <div class="flex-1">
                    <p class="text-sm font-medium text-gray-800">信息更新：iPhone 13 的参考售价已更新</p>
                    <p class="text-xs text-gray-500">系统自动更新 · 6小时前</p>
                </div>
            </div>
            
            <div class="activity-item p-4 flex items-center space-x-3">
                <div class="activity-icon activity-in">
                    <i class="fas fa-plus"></i>
                </div>
                <div class="flex-1">
                    <p class="text-sm font-medium text-gray-800">入库：华为 P50 Pro 256G 金色</p>
                    <p class="text-xs text-gray-500">入库价: ¥3,200 · 昨天</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 悬浮操作按钮 -->
    <button class="fab flex items-center justify-center text-white" onclick="showAddModal()">
        <i class="fas fa-plus text-xl"></i>
    </button>

    <!-- 底部导航栏 -->
    <nav class="bottom-nav">
        <div class="flex">
            <a href="merchant-dashboard.html" class="nav-item active flex-1 py-3 text-center">
                <i class="fas fa-home text-xl mb-1 block"></i>
                <span class="text-xs">首页</span>
            </a>
            <a href="merchant-inventory.html" class="nav-item flex-1 py-3 text-center text-gray-500">
                <i class="fas fa-boxes text-xl mb-1 block"></i>
                <span class="text-xs">库存</span>
            </a>
            <a href="merchant-profile.html" class="nav-item flex-1 py-3 text-center text-gray-500">
                <i class="fas fa-user text-xl mb-1 block"></i>
                <span class="text-xs">我的</span>
            </a>
        </div>
    </nav>

    <script>
        // 显示添加商品模态框
        function showAddModal() {
            // 这里可以显示一个模态框，包含扫码入库、手动入库等选项
            alert('功能开发中：扫码/拍照入库');
        }

        // 模拟实时数据更新
        function updateStats() {
            // 这里可以通过API获取最新的统计数据
            console.log('更新统计数据...');
        }

        // 每30秒更新一次数据
        setInterval(updateStats, 30000);

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加页面加载动画
            const cards = document.querySelectorAll('.stats-card, .alert-card, .activity-item');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
