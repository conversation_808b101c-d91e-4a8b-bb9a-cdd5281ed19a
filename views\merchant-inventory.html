<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>库存管理 - 精品二手机</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 视图切换按钮样式 */
        .view-toggle {
            background: #f3f4f6;
            border-radius: 12px;
            padding: 4px;
        }
        
        .view-toggle button {
            transition: all 0.3s ease;
            border-radius: 8px;
        }
        
        .view-toggle button.active {
            background: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            color: #667eea;
        }
        
        /* 库存卡片样式 */
        .inventory-card {
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
        }
        
        .inventory-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border-color: #667eea;
        }
        
        /* 状态标签样式 */
        .status-badge {
            font-size: 0.75rem;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 6px;
        }
        
        .status-available {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-reserved {
            background: #fef3c7;
            color: #92400e;
        }
        
        .status-sold {
            background: #f3f4f6;
            color: #6b7280;
        }
        
        /* 列表视图样式 */
        .list-item {
            transition: all 0.2s ease;
        }
        
        .list-item:hover {
            background-color: #f9fafb;
            transform: translateX(4px);
        }
        
        /* 筛选侧边栏样式 */
        .filter-sidebar {
            position: fixed;
            top: 0;
            right: -100%;
            width: 100%;
            max-width: 320px;
            height: 100vh;
            background: white;
            box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
            transition: right 0.3s ease;
            z-index: 1000;
        }
        
        .filter-sidebar.open {
            right: 0;
        }
        
        .filter-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 999;
        }
        
        .filter-overlay.open {
            opacity: 1;
            visibility: visible;
        }
        
        /* 底部导航栏 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            max-width: 448px;
            width: 100%;
            background: white;
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .nav-item {
            transition: all 0.2s ease;
        }
        
        .nav-item.active {
            color: #667eea;
        }
        
        .nav-item:not(.active):hover {
            color: #9ca3af;
        }
    </style>
</head>
<body class="bg-gray-50 pb-20">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm sticky top-0 z-50">
        <div class="max-w-md mx-auto px-4 py-4">
            <div class="flex items-center justify-between mb-4">
                <h1 class="text-xl font-bold text-gray-800">库存管理</h1>
                <div class="flex items-center space-x-2">
                    <!-- 视图切换 -->
                    <div class="view-toggle flex">
                        <button id="cardViewBtn" class="active px-3 py-2 text-sm font-medium">
                            <i class="fas fa-th-large mr-1"></i>卡片
                        </button>
                        <button id="listViewBtn" class="px-3 py-2 text-sm font-medium text-gray-600">
                            <i class="fas fa-list mr-1"></i>列表
                        </button>
                    </div>
                    <!-- 筛选按钮 -->
                    <button onclick="openFilter()" class="p-2 rounded-lg hover:bg-gray-100 transition-colors">
                        <i class="fas fa-filter text-gray-600"></i>
                    </button>
                </div>
            </div>
            
            <!-- 搜索框 -->
            <div class="relative">
                <input type="text" placeholder="搜索商品型号、品牌..." 
                       class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            </div>
        </div>
    </header>

    <!-- 库存展示区 -->
    <main class="max-w-md mx-auto px-4 py-4">
        <!-- 卡片视图 -->
        <div id="cardView" class="grid grid-cols-1 gap-4">
            <!-- 库存卡片 1 -->
            <div class="inventory-card bg-white rounded-xl p-4">
                <div class="flex space-x-4">
                    <img src="https://placehold.co/80x80/4f46e5/ffffff?text=iPhone+14" alt="iPhone 14 Pro" class="w-20 h-20 rounded-lg object-cover">
                    <div class="flex-1">
                        <div class="flex items-start justify-between mb-2">
                            <h3 class="font-semibold text-gray-800 text-sm">iPhone 14 Pro 256GB 深空黑</h3>
                            <span class="status-badge status-available">在售</span>
                        </div>
                        <div class="space-y-1 text-xs text-gray-600">
                            <div class="flex justify-between">
                                <span>入库价:</span>
                                <span class="font-medium">¥5,500</span>
                            </div>
                            <div class="flex justify-between">
                                <span>售价:</span>
                                <span class="font-medium text-red-600">¥6,299</span>
                            </div>
                            <div class="flex justify-between">
                                <span>库存天数:</span>
                                <span class="font-medium">3天</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between mt-3">
                            <div class="flex items-center space-x-2">
                                <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">99新</span>
                                <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">电池100%</span>
                            </div>
                            <button class="text-blue-600 text-sm font-medium hover:text-blue-800">
                                <i class="fas fa-edit mr-1"></i>编辑
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 库存卡片 2 -->
            <div class="inventory-card bg-white rounded-xl p-4">
                <div class="flex space-x-4">
                    <img src="https://placehold.co/80x80/f59e0b/ffffff?text=Mate+50" alt="华为 Mate 50 Pro" class="w-20 h-20 rounded-lg object-cover">
                    <div class="flex-1">
                        <div class="flex items-start justify-between mb-2">
                            <h3 class="font-semibold text-gray-800 text-sm">华为 Mate 50 Pro 512GB</h3>
                            <span class="status-badge status-reserved">已预订</span>
                        </div>
                        <div class="space-y-1 text-xs text-gray-600">
                            <div class="flex justify-between">
                                <span>入库价:</span>
                                <span class="font-medium">¥3,800</span>
                            </div>
                            <div class="flex justify-between">
                                <span>售价:</span>
                                <span class="font-medium text-red-600">¥4,599</span>
                            </div>
                            <div class="flex justify-between">
                                <span>库存天数:</span>
                                <span class="font-medium">15天</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between mt-3">
                            <div class="flex items-center space-x-2">
                                <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded">95新</span>
                                <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">电池95%</span>
                            </div>
                            <button class="text-blue-600 text-sm font-medium hover:text-blue-800">
                                <i class="fas fa-edit mr-1"></i>编辑
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 库存卡片 3 -->
            <div class="inventory-card bg-white rounded-xl p-4">
                <div class="flex space-x-4">
                    <img src="https://placehold.co/80x80/10b981/ffffff?text=小米13" alt="小米 13 Ultra" class="w-20 h-20 rounded-lg object-cover">
                    <div class="flex-1">
                        <div class="flex items-start justify-between mb-2">
                            <h3 class="font-semibold text-gray-800 text-sm">小米 13 Ultra 512GB 橄榄绿</h3>
                            <span class="status-badge status-sold">已售出</span>
                        </div>
                        <div class="space-y-1 text-xs text-gray-600">
                            <div class="flex justify-between">
                                <span>入库价:</span>
                                <span class="font-medium">¥3,500</span>
                            </div>
                            <div class="flex justify-between">
                                <span>售价:</span>
                                <span class="font-medium text-green-600">¥4,299</span>
                            </div>
                            <div class="flex justify-between">
                                <span>售出时间:</span>
                                <span class="font-medium">4小时前</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between mt-3">
                            <div class="flex items-center space-x-2">
                                <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">98新</span>
                                <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">电池98%</span>
                            </div>
                            <button class="text-gray-400 text-sm font-medium">
                                <i class="fas fa-check mr-1"></i>已完成
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 列表视图 -->
        <div id="listView" class="hidden bg-white rounded-xl shadow-sm overflow-hidden">
            <div class="divide-y divide-gray-100">
                <div class="list-item p-4 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <img src="https://placehold.co/40x40/4f46e5/ffffff?text=14P" alt="iPhone 14 Pro" class="w-10 h-10 rounded object-cover">
                        <div>
                            <h4 class="font-medium text-gray-800 text-sm">iPhone 14 Pro 256GB</h4>
                            <p class="text-xs text-gray-500">入库: ¥5,500 | 售价: ¥6,299</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <span class="status-badge status-available">在售</span>
                        <p class="text-xs text-gray-500 mt-1">3天</p>
                    </div>
                </div>
                
                <div class="list-item p-4 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <img src="https://placehold.co/40x40/f59e0b/ffffff?text=M50" alt="华为 Mate 50" class="w-10 h-10 rounded object-cover">
                        <div>
                            <h4 class="font-medium text-gray-800 text-sm">华为 Mate 50 Pro 512GB</h4>
                            <p class="text-xs text-gray-500">入库: ¥3,800 | 售价: ¥4,599</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <span class="status-badge status-reserved">已预订</span>
                        <p class="text-xs text-gray-500 mt-1">15天</p>
                    </div>
                </div>
                
                <div class="list-item p-4 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <img src="https://placehold.co/40x40/10b981/ffffff?text=13U" alt="小米 13 Ultra" class="w-10 h-10 rounded object-cover">
                        <div>
                            <h4 class="font-medium text-gray-800 text-sm">小米 13 Ultra 512GB</h4>
                            <p class="text-xs text-gray-500">入库: ¥3,500 | 售价: ¥4,299</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <span class="status-badge status-sold">已售出</span>
                        <p class="text-xs text-gray-500 mt-1">4小时前</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 筛选遮罩层 -->
    <div class="filter-overlay" onclick="closeFilter()"></div>

    <!-- 筛选侧边栏 -->
    <div class="filter-sidebar">
        <div class="p-4 border-b">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-800">筛选条件</h3>
                <button onclick="closeFilter()" class="p-2 rounded-lg hover:bg-gray-100">
                    <i class="fas fa-times text-gray-600"></i>
                </button>
            </div>
        </div>
        
        <div class="p-4 space-y-6">
            <!-- 状态筛选 -->
            <div>
                <h4 class="font-medium text-gray-800 mb-3">商品状态</h4>
                <div class="space-y-2">
                    <label class="flex items-center">
                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-700">在售</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-700">已预订</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-700">已售出</span>
                    </label>
                </div>
            </div>
            
            <!-- 品牌筛选 -->
            <div>
                <h4 class="font-medium text-gray-800 mb-3">品牌</h4>
                <div class="space-y-2">
                    <label class="flex items-center">
                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-700">苹果</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-700">华为</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-700">小米</span>
                    </label>
                </div>
            </div>
            
            <!-- 入库时间 -->
            <div>
                <h4 class="font-medium text-gray-800 mb-3">入库时间</h4>
                <select class="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    <option>全部时间</option>
                    <option>最近7天</option>
                    <option>最近30天</option>
                    <option>最近90天</option>
                </select>
            </div>
        </div>
        
        <div class="p-4 border-t">
            <div class="flex space-x-3">
                <button class="flex-1 py-2 px-4 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                    重置
                </button>
                <button class="flex-1 py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                    应用筛选
                </button>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <nav class="bottom-nav">
        <div class="flex">
            <a href="merchant-dashboard.html" class="nav-item flex-1 py-3 text-center text-gray-500">
                <i class="fas fa-home text-xl mb-1 block"></i>
                <span class="text-xs">首页</span>
            </a>
            <a href="merchant-inventory.html" class="nav-item active flex-1 py-3 text-center">
                <i class="fas fa-boxes text-xl mb-1 block"></i>
                <span class="text-xs">库存</span>
            </a>
            <a href="merchant-profile.html" class="nav-item flex-1 py-3 text-center text-gray-500">
                <i class="fas fa-user text-xl mb-1 block"></i>
                <span class="text-xs">我的</span>
            </a>
        </div>
    </nav>

    <script>
        // 视图切换功能
        const cardViewBtn = document.getElementById('cardViewBtn');
        const listViewBtn = document.getElementById('listViewBtn');
        const cardView = document.getElementById('cardView');
        const listView = document.getElementById('listView');

        cardViewBtn.addEventListener('click', () => {
            cardViewBtn.classList.add('active');
            listViewBtn.classList.remove('active');
            cardView.classList.remove('hidden');
            listView.classList.add('hidden');
        });

        listViewBtn.addEventListener('click', () => {
            listViewBtn.classList.add('active');
            cardViewBtn.classList.remove('active');
            listView.classList.remove('hidden');
            cardView.classList.add('hidden');
        });

        // 筛选侧边栏功能
        function openFilter() {
            document.querySelector('.filter-sidebar').classList.add('open');
            document.querySelector('.filter-overlay').classList.add('open');
            document.body.style.overflow = 'hidden';
        }

        function closeFilter() {
            document.querySelector('.filter-sidebar').classList.remove('open');
            document.querySelector('.filter-overlay').classList.remove('open');
            document.body.style.overflow = 'auto';
        }

        // 搜索功能
        const searchInput = document.querySelector('input[type="text"]');
        searchInput.addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            // 这里可以实现搜索过滤逻辑
            console.log('搜索:', searchTerm);
        });
    </script>
</body>
</html>
