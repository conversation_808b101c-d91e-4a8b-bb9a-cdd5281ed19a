<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - 精品二手机</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* 图表容器样式 */
        .chart-container {
            position: relative;
            height: 200px;
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        /* 时间切换按钮样式 */
        .time-toggle {
            background: #f3f4f6;
            border-radius: 8px;
            padding: 2px;
        }
        
        .time-toggle button {
            transition: all 0.3s ease;
            border-radius: 6px;
            font-size: 0.75rem;
        }
        
        .time-toggle button.active {
            background: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            color: #667eea;
        }
        
        /* 统计卡片样式 */
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 30px rgba(102, 126, 234, 0.3);
        }
        
        /* 商家信息卡片 */
        .profile-card {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
        }
        
        /* 工具列表样式 */
        .tool-item {
            transition: all 0.2s ease;
        }
        
        .tool-item:hover {
            background-color: #f9fafb;
            transform: translateX(4px);
        }
        
        .tool-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: white;
        }
        
        /* 底部导航栏 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            max-width: 448px;
            width: 100%;
            background: white;
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .nav-item {
            transition: all 0.2s ease;
        }
        
        .nav-item.active {
            color: #667eea;
        }
        
        .nav-item:not(.active):hover {
            color: #9ca3af;
        }
    </style>
</head>
<body class="bg-gray-50 pb-20">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm">
        <div class="max-w-md mx-auto px-4 py-4 flex items-center justify-between">
            <h1 class="text-xl font-bold text-gray-800">个人中心</h1>
            <button class="p-2 rounded-full hover:bg-gray-100 transition-colors">
                <i class="fas fa-cog text-gray-600 text-lg"></i>
            </button>
        </div>
    </header>

    <!-- 商家信息区 -->
    <div class="max-w-md mx-auto px-4 py-4">
        <div class="profile-card rounded-xl p-6 text-center mb-6">
            <div class="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-store text-3xl text-white"></i>
            </div>
            <h2 class="text-xl font-bold mb-1">精品二手机专营店</h2>
            <p class="text-sm opacity-90">店主：张先生 | 营业中</p>
            <div class="flex justify-center space-x-6 mt-4">
                <div class="text-center">
                    <div class="text-2xl font-bold">4.9</div>
                    <div class="text-xs opacity-80">店铺评分</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold">128</div>
                    <div class="text-xs opacity-80">总库存</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold">365</div>
                    <div class="text-xs opacity-80">营业天数</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据统计区 -->
    <div class="max-w-md mx-auto px-4 mb-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-800">数据统计</h3>
            <div class="time-toggle flex">
                <button class="active px-3 py-1 text-xs font-medium">近7天</button>
                <button class="px-3 py-1 text-xs font-medium text-gray-600">近30天</button>
                <button class="px-3 py-1 text-xs font-medium text-gray-600">本季度</button>
            </div>
        </div>

        <!-- 核心数据卡片 -->
        <div class="grid grid-cols-2 gap-4 mb-6">
            <div class="stat-card text-white p-4 rounded-xl text-center">
                <div class="text-2xl font-bold">¥12,580</div>
                <div class="text-sm opacity-90">本周销售额</div>
                <div class="text-xs opacity-75 mt-1">
                    <i class="fas fa-arrow-up mr-1"></i>+15.2%
                </div>
            </div>
            <div class="stat-card text-white p-4 rounded-xl text-center">
                <div class="text-2xl font-bold">¥3,240</div>
                <div class="text-sm opacity-90">本周利润</div>
                <div class="text-xs opacity-75 mt-1">
                    <i class="fas fa-arrow-up mr-1"></i>+8.7%
                </div>
            </div>
        </div>

        <!-- 销售趋势图表 -->
        <div class="chart-container mb-4">
            <div class="flex items-center justify-between mb-3">
                <h4 class="font-medium text-gray-800">销售趋势</h4>
                <span class="text-xs text-gray-500">近7天</span>
            </div>
            <canvas id="salesChart"></canvas>
        </div>

        <!-- 库存分布图表 -->
        <div class="chart-container mb-4">
            <div class="flex items-center justify-between mb-3">
                <h4 class="font-medium text-gray-800">库存分布</h4>
                <span class="text-xs text-gray-500">按品牌</span>
            </div>
            <canvas id="inventoryChart"></canvas>
        </div>

        <!-- 热销机型排行 -->
        <div class="bg-white rounded-xl p-4 shadow-sm">
            <h4 class="font-medium text-gray-800 mb-3">本月热销 Top 5</h4>
            <div class="space-y-3">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <span class="w-6 h-6 bg-yellow-500 text-white text-xs rounded-full flex items-center justify-center font-bold">1</span>
                        <span class="text-sm text-gray-800">iPhone 14 Pro</span>
                    </div>
                    <span class="text-sm font-medium text-gray-600">8台</span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <span class="w-6 h-6 bg-gray-400 text-white text-xs rounded-full flex items-center justify-center font-bold">2</span>
                        <span class="text-sm text-gray-800">小米 13 Ultra</span>
                    </div>
                    <span class="text-sm font-medium text-gray-600">6台</span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <span class="w-6 h-6 bg-orange-500 text-white text-xs rounded-full flex items-center justify-center font-bold">3</span>
                        <span class="text-sm text-gray-800">华为 P50 Pro</span>
                    </div>
                    <span class="text-sm font-medium text-gray-600">5台</span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <span class="w-6 h-6 bg-blue-500 text-white text-xs rounded-full flex items-center justify-center font-bold">4</span>
                        <span class="text-sm text-gray-800">iPhone 13</span>
                    </div>
                    <span class="text-sm font-medium text-gray-600">4台</span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <span class="w-6 h-6 bg-green-500 text-white text-xs rounded-full flex items-center justify-center font-bold">5</span>
                        <span class="text-sm text-gray-800">OPPO Find X5</span>
                    </div>
                    <span class="text-sm font-medium text-gray-600">3台</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 常用工具区 -->
    <div class="max-w-md mx-auto px-4 mb-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">常用工具</h3>
        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
            <div class="tool-item p-4 border-b border-gray-100 flex items-center space-x-3">
                <div class="tool-icon bg-blue-500">
                    <i class="fas fa-download"></i>
                </div>
                <div class="flex-1">
                    <h4 class="font-medium text-gray-800">导出报表</h4>
                    <p class="text-xs text-gray-500">导出销售和库存数据</p>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>
            
            <div class="tool-item p-4 border-b border-gray-100 flex items-center space-x-3">
                <div class="tool-icon bg-green-500">
                    <i class="fas fa-store"></i>
                </div>
                <div class="flex-1">
                    <h4 class="font-medium text-gray-800">店铺设置</h4>
                    <p class="text-xs text-gray-500">管理店铺信息和配置</p>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>
            
            <div class="tool-item p-4 border-b border-gray-100 flex items-center space-x-3">
                <div class="tool-icon bg-purple-500">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <div class="flex-1">
                    <h4 class="font-medium text-gray-800">数据分析</h4>
                    <p class="text-xs text-gray-500">查看详细的经营分析</p>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>
            
            <div class="tool-item p-4 border-b border-gray-100 flex items-center space-x-3">
                <div class="tool-icon bg-orange-500">
                    <i class="fas fa-question-circle"></i>
                </div>
                <div class="flex-1">
                    <h4 class="font-medium text-gray-800">帮助中心</h4>
                    <p class="text-xs text-gray-500">使用指南和常见问题</p>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>
            
            <div class="tool-item p-4 flex items-center space-x-3">
                <div class="tool-icon bg-red-500">
                    <i class="fas fa-sign-out-alt"></i>
                </div>
                <div class="flex-1">
                    <h4 class="font-medium text-gray-800">退出登录</h4>
                    <p class="text-xs text-gray-500">安全退出商家系统</p>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <nav class="bottom-nav">
        <div class="flex">
            <a href="merchant-dashboard.html" class="nav-item flex-1 py-3 text-center text-gray-500">
                <i class="fas fa-home text-xl mb-1 block"></i>
                <span class="text-xs">首页</span>
            </a>
            <a href="merchant-inventory.html" class="nav-item flex-1 py-3 text-center text-gray-500">
                <i class="fas fa-boxes text-xl mb-1 block"></i>
                <span class="text-xs">库存</span>
            </a>
            <a href="merchant-profile.html" class="nav-item active flex-1 py-3 text-center">
                <i class="fas fa-user text-xl mb-1 block"></i>
                <span class="text-xs">我的</span>
            </a>
        </div>
    </nav>

    <script>
        // 时间切换功能
        const timeButtons = document.querySelectorAll('.time-toggle button');
        timeButtons.forEach(button => {
            button.addEventListener('click', function() {
                timeButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
                // 这里可以更新图表数据
                updateCharts(this.textContent);
            });
        });

        // 初始化销售趋势图表
        const salesCtx = document.getElementById('salesChart').getContext('2d');
        const salesChart = new Chart(salesCtx, {
            type: 'line',
            data: {
                labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                datasets: [{
                    label: '销售额',
                    data: [1200, 1900, 800, 2100, 1600, 2400, 1800],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });

        // 初始化库存分布图表
        const inventoryCtx = document.getElementById('inventoryChart').getContext('2d');
        const inventoryChart = new Chart(inventoryCtx, {
            type: 'doughnut',
            data: {
                labels: ['苹果', '华为', '小米', 'OPPO', '其他'],
                datasets: [{
                    data: [45, 25, 15, 10, 5],
                    backgroundColor: [
                        '#667eea',
                        '#f59e0b',
                        '#10b981',
                        '#ef4444',
                        '#8b5cf6'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            usePointStyle: true,
                            padding: 15,
                            font: {
                                size: 12
                            }
                        }
                    }
                }
            }
        });

        // 更新图表数据
        function updateCharts(period) {
            // 根据选择的时间周期更新图表数据
            console.log('更新图表数据:', period);
            // 这里可以调用API获取对应时间段的数据
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.stat-card, .chart-container, .tool-item');
            elements.forEach((element, index) => {
                element.style.opacity = '0';
                element.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    element.style.transition = 'all 0.5s ease';
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
