<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>精品二手机 - 发现好物</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 自定义样式 */
        .sticky-filter {
            position: sticky;
            top: 0;
            z-index: 50;
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .product-card {
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .product-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .price-highlight {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .badge {
            background: linear-gradient(135deg, #00d2ff, #3a7bd5);
        }
        
        .filter-chip {
            transition: all 0.2s ease;
        }
        
        .filter-chip.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm">
        <div class="max-w-md mx-auto px-4 py-3 flex items-center justify-between">
            <div class="flex items-center space-x-2">
                <i class="fas fa-mobile-alt text-2xl text-blue-600"></i>
                <h1 class="text-xl font-bold text-gray-800">精品二手机</h1>
            </div>
            <button class="p-2 rounded-full hover:bg-gray-100 transition-colors">
                <i class="fas fa-search text-gray-600 text-lg"></i>
            </button>
        </div>
    </header>

    <!-- 横向筛选条 - 吸顶设计 -->
    <div class="sticky-filter border-b border-gray-200">
        <div class="max-w-md mx-auto px-4 py-3">
            <div class="flex space-x-3 overflow-x-auto scrollbar-hide">
                <!-- 品牌筛选 -->
                <button class="filter-chip active flex-shrink-0 px-4 py-2 rounded-full text-sm font-medium border border-gray-300">
                    <i class="fas fa-apple mr-1"></i>全部品牌
                </button>
                <button class="filter-chip flex-shrink-0 px-4 py-2 rounded-full text-sm font-medium border border-gray-300 hover:bg-gray-50">
                    苹果
                </button>
                <button class="filter-chip flex-shrink-0 px-4 py-2 rounded-full text-sm font-medium border border-gray-300 hover:bg-gray-50">
                    华为
                </button>
                <button class="filter-chip flex-shrink-0 px-4 py-2 rounded-full text-sm font-medium border border-gray-300 hover:bg-gray-50">
                    小米
                </button>
                <button class="filter-chip flex-shrink-0 px-4 py-2 rounded-full text-sm font-medium border border-gray-300 hover:bg-gray-50">
                    <i class="fas fa-sliders-h mr-1"></i>筛选
                </button>
            </div>
        </div>
    </div>

    <!-- 商品展示区 - 双列瀑布流 -->
    <main class="max-w-md mx-auto px-4 py-4">
        <div class="grid grid-cols-2 gap-4" id="productGrid">
            <!-- 商品卡片 1 -->
            <div class="product-card bg-white rounded-xl overflow-hidden cursor-pointer" onclick="goToDetail('iphone14pro')">
                <div class="relative">
                    <img src="https://placehold.co/200x240/4f46e5/ffffff?text=iPhone+14+Pro" alt="iPhone 14 Pro" class="w-full h-48 object-cover">
                    <div class="absolute top-2 left-2">
                        <span class="badge text-white text-xs px-2 py-1 rounded-full">99新</span>
                    </div>
                    <div class="absolute top-2 right-2">
                        <span class="bg-green-500 text-white text-xs px-2 py-1 rounded-full">电池100%</span>
                    </div>
                </div>
                <div class="p-3">
                    <h3 class="font-semibold text-gray-800 text-sm mb-1 line-clamp-2">iPhone 14 Pro 256GB 深空黑色</h3>
                    <div class="flex items-center justify-between">
                        <span class="price-highlight text-xl font-bold">¥6,299</span>
                        <span class="text-gray-400 text-xs line-through">¥8,999</span>
                    </div>
                    <div class="flex items-center mt-2 text-xs text-gray-500">
                        <i class="fas fa-shield-alt mr-1"></i>
                        <span>官方质检 · 180天质保</span>
                    </div>
                </div>
            </div>

            <!-- 商品卡片 2 -->
            <div class="product-card bg-white rounded-xl overflow-hidden cursor-pointer" onclick="goToDetail('huaweimate50')">
                <div class="relative">
                    <img src="https://placehold.co/200x260/f59e0b/ffffff?text=Mate+50+Pro" alt="华为 Mate 50 Pro" class="w-full h-52 object-cover">
                    <div class="absolute top-2 left-2">
                        <span class="badge text-white text-xs px-2 py-1 rounded-full">95新</span>
                    </div>
                    <div class="absolute top-2 right-2">
                        <span class="bg-yellow-500 text-white text-xs px-2 py-1 rounded-full">电池95%</span>
                    </div>
                </div>
                <div class="p-3">
                    <h3 class="font-semibold text-gray-800 text-sm mb-1 line-clamp-2">华为 Mate 50 Pro 512GB 昆仑破晓</h3>
                    <div class="flex items-center justify-between">
                        <span class="price-highlight text-xl font-bold">¥4,599</span>
                        <span class="text-gray-400 text-xs line-through">¥6,799</span>
                    </div>
                    <div class="flex items-center mt-2 text-xs text-gray-500">
                        <i class="fas fa-award mr-1"></i>
                        <span>原装配件 · 7天无理由</span>
                    </div>
                </div>
            </div>

            <!-- 商品卡片 3 -->
            <div class="product-card bg-white rounded-xl overflow-hidden cursor-pointer" onclick="goToDetail('xiaomi13ultra')">
                <div class="relative">
                    <img src="https://placehold.co/200x240/10b981/ffffff?text=小米13+Ultra" alt="小米 13 Ultra" class="w-full h-48 object-cover">
                    <div class="absolute top-2 left-2">
                        <span class="badge text-white text-xs px-2 py-1 rounded-full">98新</span>
                    </div>
                    <div class="absolute top-2 right-2">
                        <span class="bg-green-500 text-white text-xs px-2 py-1 rounded-full">电池98%</span>
                    </div>
                </div>
                <div class="p-3">
                    <h3 class="font-semibold text-gray-800 text-sm mb-1 line-clamp-2">小米 13 Ultra 512GB 橄榄绿</h3>
                    <div class="flex items-center justify-between">
                        <span class="price-highlight text-xl font-bold">¥4,299</span>
                        <span class="text-gray-400 text-xs line-through">¥5,999</span>
                    </div>
                    <div class="flex items-center mt-2 text-xs text-gray-500">
                        <i class="fas fa-camera mr-1"></i>
                        <span>徕卡影像 · 专业检测</span>
                    </div>
                </div>
            </div>

            <!-- 商品卡片 4 -->
            <div class="product-card bg-white rounded-xl overflow-hidden cursor-pointer" onclick="goToDetail('iphone13')">
                <div class="relative">
                    <img src="https://placehold.co/200x260/ef4444/ffffff?text=iPhone+13" alt="iPhone 13" class="w-full h-52 object-cover">
                    <div class="absolute top-2 left-2">
                        <span class="badge text-white text-xs px-2 py-1 rounded-full">96新</span>
                    </div>
                    <div class="absolute top-2 right-2">
                        <span class="bg-green-500 text-white text-xs px-2 py-1 rounded-full">电池92%</span>
                    </div>
                </div>
                <div class="p-3">
                    <h3 class="font-semibold text-gray-800 text-sm mb-1 line-clamp-2">iPhone 13 128GB 红色</h3>
                    <div class="flex items-center justify-between">
                        <span class="price-highlight text-xl font-bold">¥3,899</span>
                        <span class="text-gray-400 text-xs line-through">¥5,399</span>
                    </div>
                    <div class="flex items-center mt-2 text-xs text-gray-500">
                        <i class="fas fa-star mr-1"></i>
                        <span>热销机型 · 品质保证</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 加载更多指示器 -->
        <div class="text-center py-8">
            <div class="inline-flex items-center space-x-2 text-gray-500">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                <span class="text-sm">正在加载更多好物...</span>
            </div>
        </div>
    </main>

    <script>
        // 页面交互逻辑
        function goToDetail(productId) {
            // 跳转到商品详情页
            window.location.href = `customer-detail.html?id=${productId}`;
        }

        // 筛选按钮交互
        document.querySelectorAll('.filter-chip').forEach(chip => {
            chip.addEventListener('click', function() {
                // 移除其他按钮的active状态
                document.querySelectorAll('.filter-chip').forEach(c => c.classList.remove('active'));
                // 添加当前按钮的active状态
                this.classList.add('active');
            });
        });

        // 模拟无限滚动加载
        let loading = false;
        window.addEventListener('scroll', function() {
            if (loading) return;
            
            if (window.innerHeight + window.scrollY >= document.body.offsetHeight - 1000) {
                loading = true;
                // 模拟加载延迟
                setTimeout(() => {
                    // 这里可以添加更多商品卡片
                    loading = false;
                }, 1000);
            }
        });
    </script>
</body>
</html>
