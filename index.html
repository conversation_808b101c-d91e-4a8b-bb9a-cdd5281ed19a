<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二手手机库存管理小程序 - 原型展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .demo-card {
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .demo-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border-color: #667eea;
        }
        
        .customer-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .merchant-card {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }
        
        .api-card {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        
        .hero-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #ff6b6b 100%);
        }
        
        .feature-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            margin: 0 auto 16px;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 头部Hero区域 -->
    <header class="hero-bg text-white py-16">
        <div class="max-w-6xl mx-auto px-4 text-center">
            <div class="mb-8">
                <i class="fas fa-mobile-alt text-6xl mb-4"></i>
                <h1 class="text-4xl font-bold mb-4">二手手机库存管理小程序</h1>
                <p class="text-xl opacity-90 mb-8">商业级、体验一流的应用原型</p>
                <div class="flex justify-center space-x-8 text-sm">
                    <div class="text-center">
                        <div class="text-2xl font-bold">6</div>
                        <div class="opacity-80">页面原型</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold">2</div>
                        <div class="opacity-80">用户角色</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold">100%</div>
                        <div class="opacity-80">响应式设计</div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="max-w-6xl mx-auto px-4 py-12">
        <!-- 顾客端原型 -->
        <section class="mb-16">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-800 mb-4">顾客端原型</h2>
                <p class="text-gray-600">纯粹的沉浸式浏览体验，专业的精品二手导购平台</p>
            </div>
            
            <div class="grid md:grid-cols-2 gap-8">
                <div class="demo-card customer-card text-white rounded-2xl p-8">
                    <div class="feature-icon bg-white bg-opacity-20">
                        <i class="fas fa-home"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">顾客首页</h3>
                    <p class="mb-6 opacity-90">双列瀑布流商品展示，横向筛选条，激发用户"寻宝感"和"价值感"</p>
                    <div class="space-y-2 mb-6 text-sm opacity-80">
                        <div>✓ 极简顶栏设计</div>
                        <div>✓ 吸顶筛选条</div>
                        <div>✓ 无限滚动加载</div>
                        <div>✓ 视觉层次优化</div>
                    </div>
                    <a href="views/customer-home.html" target="_blank" 
                       class="inline-block bg-white text-purple-600 px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                        <i class="fas fa-external-link-alt mr-2"></i>查看原型
                    </a>
                </div>

                <div class="demo-card customer-card text-white rounded-2xl p-8">
                    <div class="feature-icon bg-white bg-opacity-20">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">商品详情页</h3>
                    <p class="mb-6 opacity-90">详尽信息展示，打消用户疑虑，建立信任的关键页面</p>
                    <div class="space-y-2 mb-6 text-sm opacity-80">
                        <div>✓ 图片轮播展示</div>
                        <div>✓ 服务承诺突出</div>
                        <div>✓ 规格参数清晰</div>
                        <div>✓ 底部操作栏</div>
                    </div>
                    <a href="views/customer-detail.html" target="_blank" 
                       class="inline-block bg-white text-purple-600 px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                        <i class="fas fa-external-link-alt mr-2"></i>查看原型
                    </a>
                </div>
            </div>
        </section>

        <!-- 商家端原型 -->
        <section class="mb-16">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-800 mb-4">商家端原型</h2>
                <p class="text-gray-600">效率优先，信息清晰，决策支持的专业管理系统</p>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="demo-card merchant-card text-white rounded-2xl p-6">
                    <div class="feature-icon bg-white bg-opacity-20">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">安全登录</h3>
                    <p class="mb-4 text-sm opacity-90">6位PIN码验证，极致纯净的安全入口</p>
                    <a href="views/merchant-login.html" target="_blank" 
                       class="inline-block bg-white text-red-600 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors">
                        查看原型
                    </a>
                </div>

                <div class="demo-card merchant-card text-white rounded-2xl p-6">
                    <div class="feature-icon bg-white bg-opacity-20">
                        <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">工作台</h3>
                    <p class="mb-4 text-sm opacity-90">核心数据卡片，智能提醒，高效操作中心</p>
                    <a href="views/merchant-dashboard.html" target="_blank" 
                       class="inline-block bg-white text-red-600 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors">
                        查看原型
                    </a>
                </div>

                <div class="demo-card merchant-card text-white rounded-2xl p-6">
                    <div class="feature-icon bg-white bg-opacity-20">
                        <i class="fas fa-boxes"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">库存管理</h3>
                    <p class="mb-4 text-sm opacity-90">双视图切换，强大筛选，状态标签清晰</p>
                    <a href="views/merchant-inventory.html" target="_blank" 
                       class="inline-block bg-white text-red-600 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors">
                        查看原型
                    </a>
                </div>

                <div class="demo-card merchant-card text-white rounded-2xl p-6">
                    <div class="feature-icon bg-white bg-opacity-20">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">数据中心</h3>
                    <p class="mb-4 text-sm opacity-90">可视化图表，销售分析，经营决策支持</p>
                    <a href="views/merchant-profile.html" target="_blank" 
                       class="inline-block bg-white text-red-600 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors">
                        查看原型
                    </a>
                </div>
            </div>
        </section>

        <!-- 技术特性 -->
        <section class="mb-16">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-800 mb-4">技术特性</h2>
                <p class="text-gray-600">现代化技术栈，完整的前后端协作方案</p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-8">
                <div class="demo-card bg-white rounded-2xl p-8 text-center">
                    <div class="feature-icon api-card">
                        <i class="fas fa-code"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-4">前端技术</h3>
                    <div class="space-y-2 text-sm text-gray-600">
                        <div>HTML5 + Tailwind CSS</div>
                        <div>Font Awesome 图标</div>
                        <div>Chart.js 数据可视化</div>
                        <div>响应式设计</div>
                        <div>移动端优化</div>
                    </div>
                </div>

                <div class="demo-card bg-white rounded-2xl p-8 text-center">
                    <div class="feature-icon api-card">
                        <i class="fas fa-database"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-4">API接口</h3>
                    <div class="space-y-2 text-sm text-gray-600">
                        <div>RESTful API 设计</div>
                        <div>Mock 数据模拟</div>
                        <div>完整接口文档</div>
                        <div>前后端分离</div>
                        <div>易于集成</div>
                    </div>
                </div>

                <div class="demo-card bg-white rounded-2xl p-8 text-center">
                    <div class="feature-icon api-card">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-4">设计理念</h3>
                    <div class="space-y-2 text-sm text-gray-600">
                        <div>产品经理视角</div>
                        <div>用户体验优先</div>
                        <div>视觉层次清晰</div>
                        <div>交互细节丰富</div>
                        <div>商业级品质</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- API文档链接 -->
        <section class="text-center">
            <div class="api-card text-white rounded-2xl p-8 inline-block">
                <div class="feature-icon bg-white bg-opacity-20">
                    <i class="fas fa-book"></i>
                </div>
                <h3 class="text-2xl font-bold mb-4">API接口文档</h3>
                <p class="mb-6 opacity-90">完整的前后端协作契约，支持Mock数据开发</p>
                <div class="space-x-4">
                    <a href="api/api.js" target="_blank" 
                       class="inline-block bg-white text-green-600 px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                        <i class="fas fa-code mr-2"></i>API封装
                    </a>
                    <a href="api/mock-api.js" target="_blank" 
                       class="inline-block bg-white text-green-600 px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                        <i class="fas fa-database mr-2"></i>Mock数据
                    </a>
                </div>
            </div>
        </section>
    </main>

    <!-- 底部信息 -->
    <footer class="bg-gray-800 text-white py-8 mt-16">
        <div class="max-w-6xl mx-auto px-4 text-center">
            <p class="text-gray-400">
                二手手机库存管理小程序原型 | 
                采用现代化技术栈 | 
                商业级用户体验设计
            </p>
        </div>
    </footer>

    <script>
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.demo-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
