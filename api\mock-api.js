// api/mock-api.js

/**
 * 模拟的手机数据库。
 * 在真实应用中，这些数据会存储在服务器的数据库里。
 * status: 'for-sale' (在售), 'sold' (已售)
 */
const mockPhoneDatabase = [
    {
      id: 1,
      brand: 'Apple',
      model: 'iPhone 14 Pro',
      storage: '256GB',
      color: '深空黑',
      condition: '99新',
      cost: 6500,
      price: 7300,
      imei: '350011223344551',
      imageUrl: 'https://images.unsplash.com/photo-1678248424135-a6e5d881b24a?q=80&w=800&h=800&fit=crop',
      notes: '电池健康度100%，国行在保，全套包装配件齐全。',
      status: 'for-sale',
      entryDate: '2025-05-20',
      specs: {
          screen: '6.1英寸',
          batteryHealth: '100%',
          network: '公开版5G',
          warranty: '官方在保'
      }
    },
    {
      id: 2,
      brand: '<PERSON><PERSON><PERSON>',
      model: 'Mate 60 Pro',
      storage: '512GB',
      color: '雅川青',
      condition: '95新',
      cost: 5800,
      price: 6500,
      imei: '350011223344552',
      imageUrl: 'https://images.unsplash.com/photo-1695943375379-6931e822215c?q=80&w=800&h=800&fit=crop',
      notes: '屏幕有轻微划痕，不影响使用。',
      status: 'for-sale',
      entryDate: '2025-05-18',
      specs: {
          screen: '6.82英寸',
          batteryHealth: '98%',
          network: '5G全网通',
          warranty: '已过保'
      }
    },
    {
      id: 3,
      brand: 'Samsung',
      model: 'S23 Ultra',
      storage: '512GB',
      color: '悠雾紫',
      condition: '9成新',
      cost: 6800,
      price: 7500,
      imei: '350011223344553',
      imageUrl: 'https://images.unsplash.com/photo-1678248424135-a6e5d881b24a?q=80&w=800&h=800&fit=crop',
      notes: '机身有轻微磕碰。',
      status: 'sold',
      entryDate: '2025-04-10',
      specs: {
          screen: '6.8英寸',
          batteryHealth: '95%',
          network: '5G全网通',
          warranty: '已过保'
      }
    },
    {
      id: 4,
      brand: 'Xiaomi',
      model: '14 Ultra',
      storage: '1TB',
      color: '龙晶蓝',
      condition: '全新',
      cost: 6500,
      price: 7200,
      imei: '350011223344554',
      imageUrl: 'https://images.unsplash.com/photo-1610792516307-ea5acd9c3b00?q=80&w=800&h=800&fit=crop',
      notes: '全新未拆封。',
      status: 'for-sale',
      entryDate: '2025-05-21',
      specs: {
          screen: '6.73英寸',
          batteryHealth: '100%',
          network: '5G全网通',
          warranty: '官方在保'
      }
    }
  ];
  
  // 模拟网络延迟的函数
  const sleep = (ms = 500) => new Promise(resolve => setTimeout(resolve, ms));
  
  // 模拟获取所有手机列表的API
  export async function getPhones() {
    await sleep();
    console.log('Mock API: Fetched all phones.', mockPhoneDatabase);
    return JSON.parse(JSON.stringify(mockPhoneDatabase)); // 返回深拷贝以防意外修改
  }
  
  // 模拟根据ID获取单个手机的API
  export async function getPhoneById(id) {
    await sleep();
    const phone = mockPhoneDatabase.find(p => p.id === parseInt(id));
    console.log(`Mock API: Fetched phone by ID ${id}.`, phone);
    return phone ? JSON.parse(JSON.stringify(phone)) : null;
  }
  
  // 模拟获取仪表盘数据的API
  export async function getDashboardStats() {
      await sleep();
      const forSalePhones = mockPhoneDatabase.filter(p => p.status === 'for-sale');
      const totalValue = forSalePhones.reduce((sum, phone) => sum + phone.cost, 0);
      const totalCount = forSalePhones.length;
      const soldToday = 1; // 假设数据
  
      const stats = {
          totalValue,
          totalCount,
          soldToday
      };
      console.log('Mock API: Fetched dashboard stats.', stats);
      return stats;
  }
  