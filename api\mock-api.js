// Mock API数据 - 二手手机库存管理系统
// 用于前端开发阶段的数据模拟

class MockAPI {
    constructor() {
        this.initMockData();
    }

    // 初始化模拟数据
    initMockData() {
        // 商品数据
        this.products = [
            {
                id: 'iphone14pro',
                title: 'iPhone 14 Pro 256GB 深空黑色',
                brand: 'Apple',
                model: 'iPhone 14 Pro',
                storage: '256GB',
                color: '深空黑色',
                condition: 99,
                batteryHealth: 100,
                price: 6299,
                originalPrice: 8999,
                images: [
                    'https://placehold.co/400x400/4f46e5/ffffff?text=iPhone+14+Pro+正面',
                    'https://placehold.co/400x400/6366f1/ffffff?text=iPhone+14+Pro+背面',
                    'https://placehold.co/400x400/8b5cf6/ffffff?text=iPhone+14+Pro+侧面',
                    'https://placehold.co/400x400/a855f7/ffffff?text=包装盒+配件'
                ],
                features: [
                    'A16仿生芯片，性能强劲',
                    '4800万像素主摄，专业影像',
                    '电池健康度100%，续航无忧',
                    '原装配件齐全，品质保证'
                ],
                specs: {
                    brand: 'Apple iPhone 14 Pro',
                    storage: '256GB',
                    color: '深空黑色',
                    screenSize: '6.1英寸',
                    processor: 'A16仿生芯片',
                    batteryHealth: '100%',
                    condition: '99新',
                    network: '5G全网通',
                    stockDate: '2024-06-28'
                },
                status: 'available',
                stockDays: 3,
                createdAt: '2024-06-28T10:00:00Z'
            },
            {
                id: 'huaweimate50',
                title: '华为 Mate 50 Pro 512GB 昆仑破晓',
                brand: 'Huawei',
                model: 'Mate 50 Pro',
                storage: '512GB',
                color: '昆仑破晓',
                condition: 95,
                batteryHealth: 95,
                price: 4599,
                originalPrice: 6799,
                images: [
                    'https://placehold.co/400x400/f59e0b/ffffff?text=Mate+50+Pro+正面',
                    'https://placehold.co/400x400/f59e0b/ffffff?text=Mate+50+Pro+背面'
                ],
                features: [
                    '麒麟9000S芯片',
                    '5000万像素超感知摄像头',
                    '原装配件齐全',
                    '7天无理由退换'
                ],
                status: 'reserved',
                stockDays: 15,
                createdAt: '2024-06-15T14:30:00Z'
            },
            {
                id: 'xiaomi13ultra',
                title: '小米 13 Ultra 512GB 橄榄绿',
                brand: 'Xiaomi',
                model: '13 Ultra',
                storage: '512GB',
                color: '橄榄绿',
                condition: 98,
                batteryHealth: 98,
                price: 4299,
                originalPrice: 5999,
                images: [
                    'https://placehold.co/400x400/10b981/ffffff?text=小米13+Ultra+正面',
                    'https://placehold.co/400x400/10b981/ffffff?text=小米13+Ultra+背面'
                ],
                features: [
                    '骁龙8 Gen 2处理器',
                    '徕卡影像系统',
                    '专业检测认证',
                    '180天质保服务'
                ],
                status: 'sold',
                soldAt: '2024-06-30T16:00:00Z',
                createdAt: '2024-06-20T09:15:00Z'
            },
            {
                id: 'iphone13',
                title: 'iPhone 13 128GB 红色',
                brand: 'Apple',
                model: 'iPhone 13',
                storage: '128GB',
                color: '红色',
                condition: 96,
                batteryHealth: 92,
                price: 3899,
                originalPrice: 5399,
                images: [
                    'https://placehold.co/400x400/ef4444/ffffff?text=iPhone+13+正面'
                ],
                features: [
                    'A15仿生芯片',
                    '双摄系统',
                    '热销机型',
                    '品质保证'
                ],
                status: 'available',
                stockDays: 8,
                createdAt: '2024-06-22T11:20:00Z'
            }
        ];

        // 库存数据
        this.inventory = [
            {
                id: 'inv001',
                productId: 'iphone14pro',
                purchasePrice: 5500,
                sellPrice: 6299,
                status: 'available',
                stockDays: 3,
                createdAt: '2024-06-28T10:00:00Z'
            },
            {
                id: 'inv002',
                productId: 'huaweimate50',
                purchasePrice: 3800,
                sellPrice: 4599,
                status: 'reserved',
                stockDays: 15,
                createdAt: '2024-06-15T14:30:00Z'
            },
            {
                id: 'inv003',
                productId: 'xiaomi13ultra',
                purchasePrice: 3500,
                sellPrice: 4299,
                status: 'sold',
                soldAt: '2024-06-30T16:00:00Z',
                createdAt: '2024-06-20T09:15:00Z'
            },
            {
                id: 'inv004',
                productId: 'iphone13',
                purchasePrice: 3200,
                sellPrice: 3899,
                status: 'available',
                stockDays: 8,
                createdAt: '2024-06-22T11:20:00Z'
            }
        ];

        // 工作台数据
        this.dashboardData = {
            todayStats: {
                inStock: 3,
                sold: 1,
                totalInventory: 128
            },
            alerts: [
                {
                    type: 'warning',
                    title: '库存预警',
                    message: 'iPhone 12 Pro 仅剩 1 台，建议及时补货',
                    icon: 'fas fa-exclamation-triangle'
                },
                {
                    type: 'info',
                    title: '热门机型',
                    message: '最近7天 iPhone 14 Pro 咨询量最高',
                    icon: 'fas fa-chart-line'
                },
                {
                    type: 'success',
                    title: '长期未售出',
                    message: '华为 Mate 40 Pro 已在库 60 天，考虑调价',
                    icon: 'fas fa-clock'
                }
            ],
            recentActivity: [
                {
                    type: 'in',
                    title: '入库：iPhone 14 Pro 256G 紫色',
                    subtitle: '入库价: ¥5,500',
                    time: '2小时前',
                    icon: 'fas fa-plus'
                },
                {
                    type: 'out',
                    title: '售出：小米 13 Ultra 512G',
                    subtitle: '售价: ¥4,800',
                    time: '4小时前',
                    icon: 'fas fa-minus'
                },
                {
                    type: 'update',
                    title: '信息更新：iPhone 13 的参考售价已更新',
                    subtitle: '系统自动更新',
                    time: '6小时前',
                    icon: 'fas fa-edit'
                },
                {
                    type: 'in',
                    title: '入库：华为 P50 Pro 256G 金色',
                    subtitle: '入库价: ¥3,200',
                    time: '昨天',
                    icon: 'fas fa-plus'
                }
            ]
        };

        // 销售统计数据
        this.salesStats = {
            '7d': {
                totalSales: 12580,
                totalProfit: 3240,
                salesTrend: [1200, 1900, 800, 2100, 1600, 2400, 1800],
                labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
            },
            '30d': {
                totalSales: 45600,
                totalProfit: 12800,
                salesTrend: [8000, 9200, 7800, 11000, 10500, 12400, 9800, 8600, 9900, 11200],
                labels: ['第1周', '第2周', '第3周', '第4周']
            }
        };

        // 库存统计数据
        this.inventoryStats = {
            brandDistribution: {
                labels: ['苹果', '华为', '小米', 'OPPO', '其他'],
                data: [45, 25, 15, 10, 5]
            },
            topSelling: [
                { name: 'iPhone 14 Pro', count: 8, rank: 1 },
                { name: '小米 13 Ultra', count: 6, rank: 2 },
                { name: '华为 P50 Pro', count: 5, rank: 3 },
                { name: 'iPhone 13', count: 4, rank: 4 },
                { name: 'OPPO Find X5', count: 3, rank: 5 }
            ]
        };

        // 商家信息
        this.merchantProfile = {
            storeName: '精品二手机专营店',
            ownerName: '张先生',
            status: '营业中',
            rating: 4.9,
            totalInventory: 128,
            businessDays: 365,
            phone: '138****8888',
            address: '北京市朝阳区xxx街道xxx号',
            businessHours: '09:00-21:00'
        };
    }

    // 模拟网络延迟
    async delay(ms = 500) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 模拟API响应
    async mockResponse(data, success = true) {
        await this.delay();

        if (success) {
            return {
                success: true,
                data: data,
                message: '请求成功',
                timestamp: new Date().toISOString()
            };
        } else {
            throw new Error('模拟API错误');
        }
    }

    // ==================== 顾客端API模拟 ====================

    async getProducts(filters = {}) {
        let products = [...this.products];

        // 应用筛选条件
        if (filters.brand) {
            products = products.filter(p => p.brand === filters.brand);
        }

        if (filters.minPrice) {
            products = products.filter(p => p.price >= filters.minPrice);
        }

        if (filters.maxPrice) {
            products = products.filter(p => p.price <= filters.maxPrice);
        }

        return this.mockResponse(products);
    }

    async getProductDetail(productId) {
        const product = this.products.find(p => p.id === productId);

        if (!product) {
            throw new Error('商品不存在');
        }

        return this.mockResponse(product);
    }

    async searchProducts(keyword, filters = {}) {
        let products = this.products.filter(p =>
            p.title.toLowerCase().includes(keyword.toLowerCase()) ||
            p.brand.toLowerCase().includes(keyword.toLowerCase()) ||
            p.model.toLowerCase().includes(keyword.toLowerCase())
        );

        return this.mockResponse(products);
    }

    // ==================== 商家端API模拟 ====================

    async merchantLogin(pinCode) {
        // 模拟PIN码验证
        const correctPin = '123456';

        if (pinCode === correctPin) {
            return this.mockResponse({
                token: 'mock-jwt-token-12345',
                merchant: this.merchantProfile
            });
        } else {
            throw new Error('PIN码错误');
        }
    }

    async getDashboardData() {
        return this.mockResponse(this.dashboardData);
    }

    async getInventory(filters = {}) {
        let inventory = [...this.inventory];

        // 合并商品信息
        const inventoryWithProducts = inventory.map(item => {
            const product = this.products.find(p => p.id === item.productId);
            return {
                ...item,
                product: product
            };
        });

        // 应用筛选条件
        if (filters.status) {
            inventory = inventoryWithProducts.filter(item => item.status === filters.status);
        }

        if (filters.brand) {
            inventory = inventoryWithProducts.filter(item =>
                item.product && item.product.brand === filters.brand
            );
        }

        return this.mockResponse(inventoryWithProducts);
    }

    async addInventoryItem(productData) {
        const newId = `inv${Date.now()}`;
        const newItem = {
            id: newId,
            ...productData,
            createdAt: new Date().toISOString()
        };

        this.inventory.push(newItem);
        return this.mockResponse(newItem);
    }

    async updateInventoryItem(itemId, productData) {
        const itemIndex = this.inventory.findIndex(item => item.id === itemId);

        if (itemIndex === -1) {
            throw new Error('库存项目不存在');
        }

        this.inventory[itemIndex] = {
            ...this.inventory[itemIndex],
            ...productData,
            updatedAt: new Date().toISOString()
        };

        return this.mockResponse(this.inventory[itemIndex]);
    }

    async deleteInventoryItem(itemId) {
        const itemIndex = this.inventory.findIndex(item => item.id === itemId);

        if (itemIndex === -1) {
            throw new Error('库存项目不存在');
        }

        this.inventory.splice(itemIndex, 1);
        return this.mockResponse({ deleted: true });
    }

    async getSalesStats(period = '7d') {
        const stats = this.salesStats[period] || this.salesStats['7d'];
        return this.mockResponse(stats);
    }

    async getInventoryStats() {
        return this.mockResponse(this.inventoryStats);
    }

    async getMerchantProfile() {
        return this.mockResponse(this.merchantProfile);
    }

    async updateMerchantProfile(profileData) {
        this.merchantProfile = {
            ...this.merchantProfile,
            ...profileData,
            updatedAt: new Date().toISOString()
        };

        return this.mockResponse(this.merchantProfile);
    }

    async getActivityLog(limit = 10) {
        const activities = this.dashboardData.recentActivity.slice(0, limit);
        return this.mockResponse(activities);
    }

    async exportReport(type, period) {
        // 模拟报表导出
        const reportData = {
            type: type,
            period: period,
            generatedAt: new Date().toISOString(),
            downloadUrl: `https://example.com/reports/${type}_${period}_${Date.now()}.xlsx`
        };

        return this.mockResponse(reportData);
    }
}

// 创建全局Mock API实例
const mockAPI = new MockAPI();

// 导出Mock API实例和兼容旧版本的函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = mockAPI;
} else {
    window.mockAPI = mockAPI;
}

// 兼容旧版本的导出函数
export async function getPhones() {
    const response = await mockAPI.getProducts();
    return response.data;
}

export async function getPhoneById(id) {
    try {
        const response = await mockAPI.getProductDetail(id);
        return response.data;
    } catch (error) {
        return null;
    }
}

export async function getDashboardStats() {
    const response = await mockAPI.getDashboardData();
    const data = response.data;

    return {
        totalValue: data.todayStats.totalInventory * 5000, // 模拟总价值
        totalCount: data.todayStats.totalInventory,
        soldToday: data.todayStats.sold
    };
}
