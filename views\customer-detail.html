<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iPhone 14 Pro - 商品详情</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 图片轮播样式 */
        .carousel-container {
            position: relative;
            overflow: hidden;
        }
        
        .carousel-track {
            display: flex;
            transition: transform 0.3s ease;
        }
        
        .carousel-slide {
            min-width: 100%;
            flex-shrink: 0;
        }
        
        .carousel-indicators {
            position: absolute;
            bottom: 16px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            space-x: 8px;
        }
        
        .indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .indicator.active {
            background: white;
            transform: scale(1.2);
        }
        
        /* 信任标签样式 */
        .trust-badge {
            background: linear-gradient(135deg, #10b981, #059669);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }
        
        /* 规格参数样式 */
        .spec-item {
            border-bottom: 1px solid #f3f4f6;
            transition: background-color 0.2s ease;
        }
        
        .spec-item:hover {
            background-color: #f9fafb;
        }
        
        /* 底部操作栏样式 */
        .bottom-action-bar {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            max-width: 448px;
            width: 100%;
            background: white;
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .cta-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }
    </style>
</head>
<body class="bg-gray-50 pb-20">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm relative z-10">
        <div class="max-w-md mx-auto px-4 py-3 flex items-center justify-between">
            <button onclick="goBack()" class="p-2 rounded-full hover:bg-gray-100 transition-colors">
                <i class="fas fa-arrow-left text-gray-600 text-lg"></i>
            </button>
            <h1 class="text-lg font-semibold text-gray-800">商品详情</h1>
            <button class="p-2 rounded-full hover:bg-gray-100 transition-colors">
                <i class="fas fa-share-alt text-gray-600 text-lg"></i>
            </button>
        </div>
    </header>

    <!-- 图片轮播区 -->
    <div class="max-w-md mx-auto">
        <div class="carousel-container bg-white" style="height: 400px;">
            <div class="carousel-track h-full" id="carouselTrack">
                <div class="carousel-slide">
                    <img src="https://placehold.co/400x400/4f46e5/ffffff?text=iPhone+14+Pro+正面" alt="iPhone 14 Pro 正面" class="w-full h-full object-cover">
                </div>
                <div class="carousel-slide">
                    <img src="https://placehold.co/400x400/6366f1/ffffff?text=iPhone+14+Pro+背面" alt="iPhone 14 Pro 背面" class="w-full h-full object-cover">
                </div>
                <div class="carousel-slide">
                    <img src="https://placehold.co/400x400/8b5cf6/ffffff?text=iPhone+14+Pro+侧面" alt="iPhone 14 Pro 侧面" class="w-full h-full object-cover">
                </div>
                <div class="carousel-slide">
                    <img src="https://placehold.co/400x400/a855f7/ffffff?text=包装盒+配件" alt="包装盒和配件" class="w-full h-full object-cover">
                </div>
            </div>
            <div class="carousel-indicators">
                <div class="indicator active" data-slide="0"></div>
                <div class="indicator" data-slide="1"></div>
                <div class="indicator" data-slide="2"></div>
                <div class="indicator" data-slide="3"></div>
            </div>
        </div>
    </div>

    <!-- 核心信息区 -->
    <div class="max-w-md mx-auto px-4 py-4">
        <div class="bg-white rounded-xl p-4 shadow-sm">
            <!-- 标题和价格 -->
            <div class="mb-4">
                <h2 class="text-xl font-bold text-gray-800 mb-2">iPhone 14 Pro 256GB 深空黑色</h2>
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-baseline space-x-2">
                        <span class="text-3xl font-bold text-red-500">¥6,299</span>
                        <span class="text-lg text-gray-400 line-through">¥8,999</span>
                    </div>
                    <div class="flex items-center space-x-1">
                        <span class="bg-gradient-to-r from-orange-400 to-red-500 text-white text-sm px-2 py-1 rounded-full">99新</span>
                        <span class="bg-green-500 text-white text-sm px-2 py-1 rounded-full">电池100%</span>
                    </div>
                </div>
            </div>

            <!-- 服务承诺 - 信任锚点 -->
            <div class="grid grid-cols-3 gap-3 mb-4">
                <div class="trust-badge text-white text-center py-3 rounded-lg">
                    <i class="fas fa-shield-check text-lg mb-1"></i>
                    <div class="text-xs font-medium">官方质检</div>
                </div>
                <div class="trust-badge text-white text-center py-3 rounded-lg">
                    <i class="fas fa-undo text-lg mb-1"></i>
                    <div class="text-xs font-medium">7天无理由</div>
                </div>
                <div class="trust-badge text-white text-center py-3 rounded-lg">
                    <i class="fas fa-tools text-lg mb-1"></i>
                    <div class="text-xs font-medium">180天质保</div>
                </div>
            </div>

            <!-- 亮点特色 -->
            <div class="border-t pt-4">
                <h3 class="text-lg font-semibold text-gray-800 mb-3">产品亮点</h3>
                <div class="space-y-2">
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-star text-yellow-500"></i>
                        <span class="text-sm text-gray-700">A16仿生芯片，性能强劲</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-camera text-blue-500"></i>
                        <span class="text-sm text-gray-700">4800万像素主摄，专业影像</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-battery-full text-green-500"></i>
                        <span class="text-sm text-gray-700">电池健康度100%，续航无忧</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-box text-purple-500"></i>
                        <span class="text-sm text-gray-700">原装配件齐全，品质保证</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 规格参数 -->
    <div class="max-w-md mx-auto px-4 py-4">
        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
            <div class="px-4 py-3 border-b bg-gray-50">
                <h3 class="text-lg font-semibold text-gray-800">详细规格</h3>
            </div>
            <div class="divide-y divide-gray-100">
                <div class="spec-item px-4 py-3 flex justify-between">
                    <span class="text-gray-600">品牌型号</span>
                    <span class="font-medium text-gray-800">Apple iPhone 14 Pro</span>
                </div>
                <div class="spec-item px-4 py-3 flex justify-between">
                    <span class="text-gray-600">存储容量</span>
                    <span class="font-medium text-gray-800">256GB</span>
                </div>
                <div class="spec-item px-4 py-3 flex justify-between">
                    <span class="text-gray-600">颜色</span>
                    <span class="font-medium text-gray-800">深空黑色</span>
                </div>
                <div class="spec-item px-4 py-3 flex justify-between">
                    <span class="text-gray-600">屏幕尺寸</span>
                    <span class="font-medium text-gray-800">6.1英寸</span>
                </div>
                <div class="spec-item px-4 py-3 flex justify-between">
                    <span class="text-gray-600">处理器</span>
                    <span class="font-medium text-gray-800">A16仿生芯片</span>
                </div>
                <div class="spec-item px-4 py-3 flex justify-between">
                    <span class="text-gray-600">电池健康度</span>
                    <span class="font-medium text-green-600">100%</span>
                </div>
                <div class="spec-item px-4 py-3 flex justify-between">
                    <span class="text-gray-600">成色等级</span>
                    <span class="font-medium text-orange-600">99新</span>
                </div>
                <div class="spec-item px-4 py-3 flex justify-between">
                    <span class="text-gray-600">网络制式</span>
                    <span class="font-medium text-gray-800">5G全网通</span>
                </div>
                <div class="spec-item px-4 py-3 flex justify-between">
                    <span class="text-gray-600">入库时间</span>
                    <span class="font-medium text-gray-800">2024-06-28</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="bottom-action-bar">
        <div class="px-4 py-3 flex items-center space-x-3">
            <button class="flex-1 bg-gray-100 text-gray-700 py-3 rounded-lg font-medium hover:bg-gray-200 transition-colors">
                <i class="fas fa-comments mr-2"></i>咨询客服
            </button>
            <button class="flex-1 cta-button text-white py-3 rounded-lg font-medium">
                <i class="fas fa-shopping-cart mr-2"></i>立即购买
            </button>
        </div>
    </div>

    <script>
        // 返回上一页
        function goBack() {
            window.history.back();
        }

        // 图片轮播功能
        let currentSlide = 0;
        const track = document.getElementById('carouselTrack');
        const indicators = document.querySelectorAll('.indicator');
        const totalSlides = indicators.length;

        // 指示器点击事件
        indicators.forEach((indicator, index) => {
            indicator.addEventListener('click', () => {
                goToSlide(index);
            });
        });

        function goToSlide(slideIndex) {
            currentSlide = slideIndex;
            const translateX = -slideIndex * 100;
            track.style.transform = `translateX(${translateX}%)`;
            
            // 更新指示器状态
            indicators.forEach((indicator, index) => {
                indicator.classList.toggle('active', index === slideIndex);
            });
        }

        // 自动轮播（可选）
        setInterval(() => {
            currentSlide = (currentSlide + 1) % totalSlides;
            goToSlide(currentSlide);
        }, 5000);

        // 触摸滑动支持
        let startX = 0;
        let currentX = 0;
        let isDragging = false;

        track.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            isDragging = true;
        });

        track.addEventListener('touchmove', (e) => {
            if (!isDragging) return;
            currentX = e.touches[0].clientX;
        });

        track.addEventListener('touchend', () => {
            if (!isDragging) return;
            isDragging = false;
            
            const diffX = startX - currentX;
            if (Math.abs(diffX) > 50) {
                if (diffX > 0 && currentSlide < totalSlides - 1) {
                    goToSlide(currentSlide + 1);
                } else if (diffX < 0 && currentSlide > 0) {
                    goToSlide(currentSlide - 1);
                }
            }
        });
    </script>
</body>
</html>
